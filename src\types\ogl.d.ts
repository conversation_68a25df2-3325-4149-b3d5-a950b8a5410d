// OGL library type declarations
declare module 'ogl' {
  export class Renderer {
    gl: WebGLRenderingContext;
    dpr: number;
    constructor(options?: {
      canvas?: HTMLCanvasElement;
      width?: number;
      height?: number;
      dpr?: number;
      alpha?: boolean;
      depth?: boolean;
      stencil?: boolean;
      antialias?: boolean;
      premultipliedAlpha?: boolean;
      preserveDrawingBuffer?: boolean;
      powerPreference?: string;
      autoClear?: boolean;
      webgl?: number;
    });
    setSize(width: number, height: number): void;
    render(options: { scene: any; camera?: any }): void;
  }

  export class Program {
    constructor(
      gl: WebGLRenderingContext,
      options: {
        vertex: string;
        fragment: string;
        uniforms?: any;
      }
    );
  }

  export class Triangle {
    constructor(gl: WebGLRenderingContext);
  }

  export class Mesh {
    constructor(
      gl: WebGLRenderingContext,
      options: {
        geometry: any;
        program: any;
      }
    );
  }
}
